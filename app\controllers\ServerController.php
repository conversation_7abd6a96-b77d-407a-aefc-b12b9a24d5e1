<?php
/**
 * Server Controller
 */

class ServerController extends Controller
{
    private $serverModel;
    private $clientModel;
    private $mediatorModel;
    
    public function __construct()
    {
        parent::__construct();
        $this->serverModel = new Server();
        $this->clientModel = new Client();
        $this->mediatorModel = new Mediator();
    }
    
    /**
     * Display servers list
     */
    public function index()
    {
        $page = $_GET['page'] ?? 1;
        $search = $_GET['search'] ?? '';
        $filter = $_GET['filter'] ?? '';
        
        if ($search) {
            $servers = $this->serverModel->searchServers($search);
            $pagination = null;
        } else {
            $conditions = [];
            if ($filter === 'active') {
                $conditions['status'] = 'active';
            } elseif ($filter === 'expired') {
                $conditions['status'] = 'expired';
            } elseif ($filter === 'expiring') {
                $servers = $this->serverModel->getExpiringServers(30);
                $pagination = null;
            } else {
                $servers = $this->serverModel->getServersWithClients($conditions, 'expiry_date ASC');
                $pagination = null;
            }
            
            if ($filter !== 'expiring' && empty($servers)) {
                $result = $this->serverModel->paginate($page, 20, $conditions, 'expiry_date ASC');
                $servers = $result['data'];
                $pagination = $result['pagination'];
                
                // Add client names
                foreach ($servers as &$server) {
                    $client = $this->clientModel->find($server['client_id']);
                    $server['client_name'] = $client['name'] ?? 'Unknown';
                }
            }
        }
        
        echo $this->view('servers/index', [
            'title' => 'Servers',
            'servers' => $servers,
            'pagination' => $pagination,
            'search' => $search,
            'filter' => $filter,
            'flash_messages' => $this->getFlashMessages()
        ]);
    }
    
    /**
     * Show create server form
     */
    public function create()
    {
        $clients = $this->clientModel->getActiveClients();
        $mediators = $this->mediatorModel->getActiveMediators();
        $serverTypes = $this->serverModel->getServerTypes();

        // Get all domains for client-domain mapping
        $domainModel = new Domain();
        $allDomains = $domainModel->all();

        echo $this->view('servers/create', [
            'title' => 'Add New Server',
            'clients' => $clients,
            'mediators' => $mediators,
            'server_types' => $serverTypes,
            'all_domains' => $allDomains,
            'validation_errors' => $this->getValidationErrors(),
            'old_input' => $this->getOldInput()
        ]);
    }
    
    /**
     * Store new server
     */
    public function store()
    {
        $this->checkCsrfToken();
        
        $rules = [
            'client_id' => 'required|numeric',
            'domain_id' => 'required|numeric',
            'server_type' => 'required',
            'purchase_date' => 'required|date',
            'years' => 'required|numeric|min:1|max:5',
            'current_cost' => 'numeric'
        ];
        
        if (!$this->validate($_POST, $rules)) {
            $this->redirect('/servers/create');
        }
        
        // Calculate expiry date based on purchase date + years
        $purchaseDate = new DateTime($_POST['purchase_date']);
        $years = (int)$_POST['years'];
        $expiryDate = clone $purchaseDate;
        $expiryDate->add(new DateInterval("P{$years}Y"));

        // Get domain name for server name
        $domainModel = new Domain();
        $domain = $domainModel->find($_POST['domain_id']);
        $serverName = $domain ? $domain['domain_name'] . ' Server' : 'Server';

        $data = [
            'client_id' => $_POST['client_id'],
            'mediator_id' => $_POST['mediator_id'] ?? null,
            'server_name' => $serverName,
            'domain_id' => $_POST['domain_id'],
            'provider' => $_POST['provider'] ?? null,
            'server_type' => $_POST['server_type'],
            'ip_address' => $_POST['ip_address'] ?? null,
            'purchase_date' => $_POST['purchase_date'],
            'expiry_date' => $expiryDate->format('Y-m-d'),
            'current_cost' => $_POST['current_cost'] ?? 0,
            'auto_renewal' => isset($_POST['auto_renewal']) ? 1 : 0,
            'specifications' => $_POST['specifications'] ?? null,
            'status' => 'active',
            'notes' => $_POST['notes'] ?? null
        ];
        
        $serverId = $this->serverModel->create($data);
        
        if ($serverId) {
            $this->setFlashMessage('Server added successfully!', 'success');
            $this->redirect('/servers/' . $serverId);
        } else {
            $this->setFlashMessage('Failed to add server.', 'error');
            $this->redirect('/servers/create');
        }
    }
    
    /**
     * Show server details
     */
    public function show($id)
    {
        $server = $this->serverModel->getServerWithRelations($id);
        
        if (!$server) {
            $this->setFlashMessage('Server not found.', 'error');
            $this->redirect('/servers');
        }
        
        echo $this->view('servers/show', [
            'title' => 'Server Details - ' . $server['server_name'],
            'server' => $server,
            'flash_messages' => $this->getFlashMessages()
        ]);
    }
    
    /**
     * Show edit server form
     */
    public function edit($id)
    {
        $server = $this->serverModel->find($id);
        
        if (!$server) {
            $this->setFlashMessage('Server not found.', 'error');
            $this->redirect('/servers');
        }
        
        $clients = $this->clientModel->getActiveClients();
        $mediators = $this->mediatorModel->getActiveMediators();
        $serverTypes = $this->serverModel->getServerTypes();
        
        echo $this->view('servers/edit', [
            'title' => 'Edit Server - ' . $server['server_name'],
            'server' => $server,
            'clients' => $clients,
            'mediators' => $mediators,
            'server_types' => $serverTypes,
            'validation_errors' => $this->getValidationErrors(),
            'old_input' => $this->getOldInput()
        ]);
    }
    
    /**
     * Update server
     */
    public function update($id)
    {
        $this->checkCsrfToken();
        
        $server = $this->serverModel->find($id);
        if (!$server) {
            $this->setFlashMessage('Server not found.', 'error');
            $this->redirect('/servers');
        }
        
        $rules = [
            'client_id' => 'required|numeric',
            'server_name' => 'required|max:100',
            'server_type' => 'required',
            'purchase_date' => 'required|date',
            'expiry_date' => 'required|date',
            'current_cost' => 'numeric'
        ];
        
        if (!$this->validate($_POST, $rules)) {
            $this->redirect('/servers/' . $id . '/edit');
        }
        
        $data = [
            'client_id' => $_POST['client_id'],
            'mediator_id' => $_POST['mediator_id'] ?? null,
            'server_name' => $_POST['server_name'],
            'provider' => $_POST['provider'] ?? null,
            'server_type' => $_POST['server_type'],
            'ip_address' => $_POST['ip_address'] ?? null,
            'purchase_date' => $_POST['purchase_date'],
            'expiry_date' => $_POST['expiry_date'],
            'current_cost' => $_POST['current_cost'] ?? 0,
            'auto_renewal' => isset($_POST['auto_renewal']) ? 1 : 0,
            'specifications' => $_POST['specifications'] ?? null,
            'status' => $_POST['status'] ?? 'active',
            'notes' => $_POST['notes'] ?? null
        ];
        
        if ($this->serverModel->update($id, $data)) {
            $this->setFlashMessage('Server updated successfully!', 'success');
            $this->redirect('/servers/' . $id);
        } else {
            $this->setFlashMessage('Failed to update server.', 'error');
            $this->redirect('/servers/' . $id . '/edit');
        }
    }
    
    /**
     * Delete server
     */
    public function delete($id)
    {
        $this->checkCsrfToken();
        
        $server = $this->serverModel->find($id);
        if (!$server) {
            $this->setFlashMessage('Server not found.', 'error');
            $this->redirect('/servers');
        }
        
        if ($this->serverModel->delete($id)) {
            $this->setFlashMessage('Server deleted successfully!', 'success');
        } else {
            $this->setFlashMessage('Failed to delete server.', 'error');
        }
        
        $this->redirect('/servers');
    }
    
    /**
     * Export servers data
     */
    public function export()
    {
        $format = $_GET['format'] ?? 'csv';
        $servers = $this->serverModel->getServersWithClients([], 'server_name ASC');
        
        if ($format === 'csv') {
            $this->exportCSV($servers);
        } else {
            $this->setFlashMessage('Invalid export format.', 'error');
            $this->redirect('/servers');
        }
    }
    
    /**
     * Export servers to CSV
     */
    private function exportCSV($servers)
    {
        $filename = 'servers_' . date('Y-m-d_H-i-s') . '.csv';
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        // CSV headers
        fputcsv($output, [
            'Server Name', 'Client', 'Provider', 'Type', 'IP Address', 
            'Purchase Date', 'Expiry Date', 'Renewal Cost', 'Status', 'Days Until Expiry'
        ]);
        
        // CSV data
        foreach ($servers as $server) {
            $daysUntilExpiry = Helper::daysBetween(date('Y-m-d'), $server['expiry_date']);
            
            fputcsv($output, [
                $server['server_name'],
                $server['client_name'],
                $server['provider'],
                $server['server_type'],
                $server['ip_address'],
                $server['purchase_date'],
                $server['expiry_date'],
                $server['current_cost'],
                $server['status'],
                $daysUntilExpiry
            ]);
        }
        
        fclose($output);
        exit;
    }
}
